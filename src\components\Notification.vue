<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'

export interface NotificationProps {
  type?: 'success' | 'warning' | 'info' | 'error'
  title?: string
  message: string
  duration?: number
  showClose?: boolean
  onClose?: () => void
}

const props = withDefaults(defineProps<NotificationProps>(), {
  type: 'info',
  title: '',
  duration: 4500,
  showClose: true
})

const emit = defineEmits<{
  close: []
}>()

const visible = ref(false)
const timer = ref<number | null>(null)

const iconMap = {
  success: '✓',
  warning: '⚠',
  info: 'ℹ',
  error: '✕'
}

const close = () => {
  visible.value = false
  if (timer.value) {
    clearTimeout(timer.value)
  }
  setTimeout(() => {
    emit('close')
    props.onClose?.()
  }, 300)
}

const startTimer = () => {
  if (props.duration > 0) {
    timer.value = setTimeout(() => {
      close()
    }, props.duration)
  }
}

const clearTimer = () => {
  if (timer.value) {
    clearTimeout(timer.value)
    timer.value = null
  }
}

onMounted(async () => {
  await nextTick()
  visible.value = true
  startTimer()
})
</script>

<template>
  <Transition name="notification" appear>
    <div
      v-if="visible"
      class="notification"
      :class="[`notification--${type}`]"
      @mouseenter="clearTimer"
      @mouseleave="startTimer"
    >
      <div class="notification__icon">
        {{ iconMap[type] }}
      </div>
      <div class="notification__content">
        <div v-if="title" class="notification__title">{{ title }}</div>
        <div class="notification__message">{{ message }}</div>
      </div>
      <button
        v-if="showClose"
        class="notification__close"
        @click="close"
      >
        ×
      </button>
    </div>
  </Transition>
</template>

<style scoped>
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 330px;
  padding: 14px 26px 14px 13px;
  border-radius: 8px;
  border: 1px solid #ebeef5;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: opacity 0.3s, transform 0.3s, right 0.3s;
  overflow: hidden;
  z-index: 9999;
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.notification__icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  color: white;
  flex-shrink: 0;
  margin-top: 1px;
}

.notification__content {
  flex: 1;
  min-width: 0;
}

.notification__title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 6px;
  line-height: 24px;
}

.notification__message {
  font-size: 14px;
  color: #606266;
  line-height: 21px;
  word-wrap: break-word;
}

.notification__close {
  position: absolute;
  top: 18px;
  right: 15px;
  background: none;
  border: none;
  font-size: 16px;
  color: #909399;
  cursor: pointer;
  line-height: 1;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification__close:hover {
  color: #606266;
}

/* 不同类型的样式 */
.notification--success {
  border-color: #e1f3d8;
  background-color: #f0f9ff;
}

.notification--success .notification__icon {
  background-color: #67c23a;
}

.notification--warning {
  border-color: #faecd8;
  background-color: #fdf6ec;
}

.notification--warning .notification__icon {
  background-color: #e6a23c;
}

.notification--info {
  border-color: #d3e3fd;
  background-color: #ecf5ff;
}

.notification--info .notification__icon {
  background-color: #409eff;
}

.notification--error {
  border-color: #fde2e2;
  background-color: #fef0f0;
}

.notification--error .notification__icon {
  background-color: #f56c6c;
}

/* 动画效果 */
.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

@media (max-width: 768px) {
  .notification {
    width: calc(100vw - 40px);
    right: 20px;
    left: 20px;
  }
}
</style>
