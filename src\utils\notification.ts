import { createApp, h } from 'vue'
import Notification from '../components/Notification.vue'
import type { NotificationProps } from '../components/Notification.vue'

interface NotificationInstance {
  id: string
  vm: any
  container: HTMLElement
}

class NotificationManager {
  private instances: NotificationInstance[] = []
  private seed = 1

  private getContainer(): HTMLElement {
    let container = document.querySelector('.notification-container')
    if (!container) {
      container = document.createElement('div')
      container.className = 'notification-container'
      ;(container as HTMLElement).style.cssText = `
        position: fixed;
        top: 0;
        right: 0;
        z-index: 9999;
        pointer-events: none;
      `
      document.body.appendChild(container)
    }
    return container as HTMLElement
  }

  private updatePositions() {
    this.instances.forEach((instance, index) => {
      const offset = index * 70 + 20
      instance.container.style.top = `${offset}px`
    })
  }

  show(options: Omit<NotificationProps, 'onClose'> & { onClose?: () => void }) {
    const id = `notification_${this.seed++}`
    const container = document.createElement('div')
    ;(container as HTMLElement).style.cssText = `
      position: absolute;
      top: 20px;
      right: 20px;
      pointer-events: auto;
      transition: top 0.3s ease;
    `

    const onClose = () => {
      this.close(id)
      options.onClose?.()
    }

    const app = createApp({
      render() {
        return h(Notification, {
          ...options,
          onClose
        })
      }
    })

    const vm = app.mount(container)
    this.getContainer().appendChild(container)

    const instance: NotificationInstance = {
      id,
      vm,
      container
    }

    this.instances.push(instance)
    this.updatePositions()

    return {
      close: () => this.close(id)
    }
  }

  close(id: string) {
    const index = this.instances.findIndex(instance => instance.id === id)
    if (index === -1) return

    const instance = this.instances[index]
    this.instances.splice(index, 1)

    // 延迟移除 DOM，等待动画完成
    setTimeout(() => {
      if (instance.container.parentNode) {
        instance.container.parentNode.removeChild(instance.container)
      }
      instance.vm.$destroy?.()
    }, 300)

    this.updatePositions()
  }

  closeAll() {
    this.instances.forEach(instance => {
      if (instance.container.parentNode) {
        instance.container.parentNode.removeChild(instance.container)
      }
      instance.vm.$destroy?.()
    })
    this.instances = []
  }

  success(message: string, title?: string, options?: Partial<NotificationProps>) {
    return this.show({
      type: 'success',
      message,
      title,
      ...options
    })
  }

  warning(message: string, title?: string, options?: Partial<NotificationProps>) {
    return this.show({
      type: 'warning',
      message,
      title,
      ...options
    })
  }

  info(message: string, title?: string, options?: Partial<NotificationProps>) {
    return this.show({
      type: 'info',
      message,
      title,
      ...options
    })
  }

  error(message: string, title?: string, options?: Partial<NotificationProps>) {
    return this.show({
      type: 'error',
      message,
      title,
      ...options
    })
  }
}

// 创建全局实例
const notification = new NotificationManager()

export default notification
export { NotificationManager }
export type { NotificationProps }
