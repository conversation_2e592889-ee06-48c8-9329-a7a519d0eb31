// API 配置文件

// 环境配置
export const API_CONFIG = {
  // 开发环境配置
  development: {
    baseURL: '/api', // 使用 Vite 代理
    timeout: 10000,
  },
  // 生产环境配置
  production: {
    baseURL: 'http://172.22.24.47:5000/api', // 替换为实际的生产环境API地址
    timeout: 15000,
  },
  // 测试环境配置
  test: {
    baseURL: '/api',
    timeout: 5000,
  }
}

// 获取当前环境
export const getCurrentEnv = (): keyof typeof API_CONFIG => {
  return (import.meta.env.MODE as keyof typeof API_CONFIG) || 'development'
}

// 获取当前环境的API配置
export const getApiConfig = () => {
  const env = getCurrentEnv()
  return API_CONFIG[env]
}

// 默认请求头
export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
}

// API 端点常量
export const API_ENDPOINTS = {
  // 项目相关
  PROJECTS: '/projects',
  PROJECT_BACKUP: (projectName: string) => `/backup/${projectName}`,
  PROJECT_LOGS: (projectName: string) => `/logs/${projectName}`,
  PROJECT_DELETE: (projectName: string) => `/delete/${projectName}`,
  
  // 可以在这里添加更多端点
  // USER: '/user',
  // AUTH: '/auth',
} as const

export type ApiEndpoint = typeof API_ENDPOINTS[keyof typeof API_ENDPOINTS]
