import { getApiConfig, DEFAULT_HEADERS, API_ENDPOINTS } from './config'

// 获取API配置
const apiConfig = getApiConfig()

// 通用请求方法
class ApiClient {
  private baseURL: string
  // private timeout: number

  constructor(baseURL: string = apiConfig.baseURL) {
    this.baseURL = baseURL
    // this.timeout = timeout
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.baseURL}${endpoint}`

    const config: RequestInit = {
      headers: {
        ...DEFAULT_HEADERS,
        ...options.headers
      },
      ...options
    }

    try {
      const response = await fetch(url, config)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      console.error('API request failed:', error)
      throw error
    }
  }

  // GET 请求
  async get<T>(endpoint: string, options?: RequestInit): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET', ...options })
  }

  // POST 请求
  async post<T>(endpoint: string, data?: any, options?: RequestInit): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
      ...options
    })
  }

  // PUT 请求
  async put<T>(endpoint: string, data?: any, options?: RequestInit): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
      ...options
    })
  }

  // DELETE 请求
  async delete<T>(endpoint: string, options?: RequestInit): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE', ...options })
  }
}

// 创建 API 客户端实例
const apiClient = new ApiClient()

// 项目相关接口类型定义
export interface Project {
  id: number
  name: string
  http_url_to_repo: string
  status?: string
  last_sync?: string
}

export interface LogEntry {
  timestamp: string
  message: string
  level: 'info' | 'error' | 'success'
}

export interface CreateProjectRequest {
  id: string
  name: string
  http_url_to_repo: string
}

export interface HttpResponse<T = any> {
  code: number
  msg: string
  data: T
}

// 项目管理 API
export const projectApi = {
  // 获取所有项目
  getProjects(): Promise<HttpResponse<Project[]>> {
    return apiClient.get<HttpResponse<Project[]>>(API_ENDPOINTS.PROJECTS)
  },

  // 添加新项目
  createProject(project: CreateProjectRequest): Promise<HttpResponse<Project>> {
    return apiClient.post<HttpResponse<Project>>(API_ENDPOINTS.PROJECTS, project)
  },

  // 触发项目备份
  triggerBackup(projectName: string): Promise<HttpResponse> {
    return apiClient.post<HttpResponse>(API_ENDPOINTS.PROJECT_BACKUP(projectName))
  },

  // 获取项目日志
  getProjectLogs(projectName: string): Promise<HttpResponse<LogEntry[]>> {
    return apiClient.get<HttpResponse<LogEntry[]>>(API_ENDPOINTS.PROJECT_LOGS(projectName))
  },

  // 删除项目
  deleteProject(projectName: string): Promise<HttpResponse> {
    return apiClient.post<HttpResponse>(API_ENDPOINTS.PROJECT_DELETE(projectName))
  }
}

// 导出 API 客户端和项目 API
export { apiClient }
export default projectApi
